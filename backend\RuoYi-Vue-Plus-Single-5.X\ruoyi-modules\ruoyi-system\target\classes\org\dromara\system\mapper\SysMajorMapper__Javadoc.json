{"doc": " 专业管理Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByMajorCode", "paramTypes": ["java.lang.String"], "doc": " 根据专业编码查询专业信息\n\n @param majorCode 专业编码\n @return 专业信息\n"}, {"name": "selectEnabledMajorList", "paramTypes": [], "doc": " 查询启用状态的专业列表\n\n @return 专业列表\n"}, {"name": "selectByMajorNameLike", "paramTypes": ["java.lang.String"], "doc": " 根据专业名称模糊查询\n\n @param majorName 专业名称\n @return 专业列表\n"}, {"name": "countQuestionBanksByMajorId", "paramTypes": ["java.lang.Long"], "doc": " 统计专业下的题库数量\n\n @param majorId 专业ID\n @return 题库数量\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 批量更新专业状态\n\n @param majorIds 专业ID列表\n @param status   状态\n @return 更新数量\n"}, {"name": "selectMaxSort", "paramTypes": [], "doc": " 获取最大排序值\n\n @return 最大排序值\n"}], "constructors": []}