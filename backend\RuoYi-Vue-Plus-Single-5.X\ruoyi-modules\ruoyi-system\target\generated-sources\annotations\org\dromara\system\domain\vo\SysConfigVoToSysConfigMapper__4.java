package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__70;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysConfig;
import org.dromara.system.domain.SysConfigToSysConfigVoMapper__4;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__70.class,
    uses = {SysConfigToSysConfigVoMapper__4.class},
    imports = {}
)
public interface SysConfigVoToSysConfigMapper__4 extends BaseMapper<SysConfigVo, SysConfig> {
}
