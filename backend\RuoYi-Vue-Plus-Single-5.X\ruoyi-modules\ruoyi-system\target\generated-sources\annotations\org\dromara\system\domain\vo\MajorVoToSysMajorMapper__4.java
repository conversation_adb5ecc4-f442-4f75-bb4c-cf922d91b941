package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__70;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysMajor;
import org.dromara.system.domain.SysMajorToMajorVoMapper__4;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__70.class,
    uses = {SysMajorToMajorVoMapper__4.class},
    imports = {}
)
public interface MajorVoToSysMajorMapper__4 extends BaseMapper<MajorVo, SysMajor> {
}
