package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__70;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysOssConfig;
import org.dromara.system.domain.SysOssConfigToSysOssConfigVoMapper__4;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__70.class,
    uses = {SysOssConfigToSysOssConfigVoMapper__4.class},
    imports = {}
)
public interface SysOssConfigVoToSysOssConfigMapper__4 extends BaseMapper<SysOssConfigVo, SysOssConfig> {
}
