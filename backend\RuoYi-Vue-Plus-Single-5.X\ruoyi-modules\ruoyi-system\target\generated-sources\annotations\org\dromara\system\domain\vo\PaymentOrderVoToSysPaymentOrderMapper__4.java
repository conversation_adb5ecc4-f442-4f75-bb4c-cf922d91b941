package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__70;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysPaymentOrder;
import org.dromara.system.domain.SysPaymentOrderToPaymentOrderVoMapper__4;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__70.class,
    uses = {SysPaymentOrderToPaymentOrderVoMapper__4.class},
    imports = {}
)
public interface PaymentOrderVoToSysPaymentOrderMapper__4 extends BaseMapper<PaymentOrderVo, SysPaymentOrder> {
}
