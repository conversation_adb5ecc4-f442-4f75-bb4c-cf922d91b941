package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__70;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.PaymentOrderBoToSysPaymentOrderMapper__4;
import org.dromara.system.domain.vo.PaymentOrderVo;
import org.dromara.system.domain.vo.PaymentOrderVoToSysPaymentOrderMapper__4;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__70.class,
    uses = {PaymentOrderBoToSysPaymentOrderMapper__4.class,PaymentOrderVoToSysPaymentOrderMapper__4.class},
    imports = {}
)
public interface SysPaymentOrderToPaymentOrderVoMapper__4 extends BaseMapper<SysPaymentOrder, PaymentOrderVo> {
}
