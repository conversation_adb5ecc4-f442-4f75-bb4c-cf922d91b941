package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__70;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.MajorBoToSysMajorMapper__4;
import org.dromara.system.domain.vo.MajorVo;
import org.dromara.system.domain.vo.MajorVoToSysMajorMapper__4;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__70.class,
    uses = {MajorVoToSysMajorMapper__4.class,MajorBoToSysMajorMapper__4.class},
    imports = {}
)
public interface SysMajorToMajorVoMapper__4 extends BaseMapper<SysMajor, MajorVo> {
}
